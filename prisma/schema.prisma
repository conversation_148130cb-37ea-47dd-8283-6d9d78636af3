// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Result {
  id               String   @id @default(uuid())
  inputText        String // The transcript from the voice recording
  analysisText     String // The AI-generated insights
  analyzedEmotions String? // JSON string of the emotions that were analyzed
  analysisType     String   @default("original") // Type of analysis performed
  chatData         String? // JSON string of chat conversations with voice and emotions
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("results")
}

model Feedback {
  id        String   @id @default(uuid())
  resultId  String? // Optional reference to the result being reviewed
  email     String? // Optional email for follow-up
  feedback  String // The feedback text
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("feedback")
}
