export interface EmotionScore {
  name: string;
  score: number;
}

export interface SentenceEmotion {
  sentence: string;
  emotions: EmotionScore[];
  startTime?: number;
  endTime?: number;
}

export type AnalysisType = "original" | "sentence-level" | "actions";
export type DisplayType = "standard" | "sentence-level";

export interface AnalysisSettings {
  emotionThreshold: number; // Minimum score (0-1) to include an emotion
  maxEmotions: number; // Maximum number of emotions to include (1-10)
  analysisType: AnalysisType; // Type of analysis to perform
}

export interface AnalysisData {
  transcript: string;
  emotions: EmotionScore[];
  analyzedEmotions: EmotionScore[];
  sentenceEmotions?: SentenceEmotion[]; // For sentence-level analysis
  insights: string;
  analysisType: AnalysisType;
  displayType?: DisplayType; // How to display the emotions
}

export interface FeedbackData {
  resultId?: string;
  email?: string;
  feedback: string;
}

// Chat-related types
export interface ChatMessage {
  id: string;
  role: "user" | "assistant";
  content: string;
  emotions?: EmotionScore[]; // For user messages with voice input
  timestamp: Date;
}

export interface ChatData {
  messages: ChatMessage[];
}

// Analysis configuration interfaces
export interface HumeModelConfig {
  prosody?: {
    granularity?: "utterance" | "sentence";
  };
  burst?: Record<string, unknown>;
}

export interface AnalysisConfig {
  id: AnalysisType;
  name: string;
  description: string;
  promptTemplate: string;
  humeConfig?: HumeModelConfig; // Hume API specific configuration
}

export interface InsightGenerationConfig {
  systemPrompt: string;
  includeActions?: boolean;
  useSentenceLevel?: boolean;
}
