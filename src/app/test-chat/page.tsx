'use client';

import { useState } from 'react';
import { ChatMessage, AnalysisData, EmotionScore } from '@/types/analysis';
import toast from 'react-hot-toast';

// Mock analysis data for testing
const mockAnalysisData: AnalysisData = {
  transcript: "I feel really excited about this new project but also a bit nervous about the challenges ahead.",
  emotions: [
    { name: "Excitement", score: 0.852 },
    { name: "Nervousness", score: 0.721 },
    { name: "Anticipation", score: 0.689 },
    { name: "Hope", score: 0.634 },
    { name: "Curiosity", score: 0.598 },
  ],
  analyzedEmotions: [
    { name: "Excitement", score: 0.852 },
    { name: "Nervousness", score: 0.721 },
    { name: "Anticipation", score: 0.689 },
  ],
  insights: "The user shows a healthy balance of enthusiasm and caution, indicating emotional maturity and readiness for growth. This combination suggests they are well-prepared to handle challenges while maintaining optimism.",
  analysisType: "original",
  displayType: "standard"
};

export default function TestChatPage() {
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isChatLoading, setIsChatLoading] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const generateAnalysisContext = () => {
    const top10Emotions = mockAnalysisData.emotions.slice(0, 10)
      .map((emotion, index) => `${index + 1}. ${emotion.name} (${(emotion.score * 100).toFixed(1)}%)`)
      .join('\n');

    let context = `You are an expert emotional intelligence coach. The user has just completed a voice emotion analysis. Here are their results:

ORIGINAL TRANSCRIPT:
"${mockAnalysisData.transcript}"

TOP 10 EMOTIONS DETECTED:
${top10Emotions}

AI INSIGHTS GENERATED:
${mockAnalysisData.insights}

ANALYSIS TYPE: ${mockAnalysisData.analysisType}`;

    context += `\n\nThe user will now chat with you using voice messages. Each message will include both their spoken words and the emotions detected in their voice. Please provide thoughtful, empathetic responses that acknowledge both their words and emotional state. Help them explore their feelings and provide insights about their emotional patterns.`;

    return context;
  };

  const sendChatMessage = async (content: string, currentMessages: ChatMessage[]) => {
    setIsChatLoading(true);

    try {
      // Create context from initial analysis
      const analysisContext = generateAnalysisContext();
      console.log('🔍 Analysis context generated:', analysisContext.substring(0, 200) + '...');

      // Build messages array with context
      const messages = [];

      // Add context message if this is the first message
      if (currentMessages.length === 0) {
        messages.push({
          role: 'user',
          content: analysisContext,
        });
        console.log('📝 Added context as first message');
      }

      // Add previous chat messages
      messages.push(...currentMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
      })));
      console.log('💬 Added previous messages:', currentMessages.length);

      // Add current user message
      messages.push({
        role: 'user',
        content,
      });
      console.log('🗣️ Added current message:', content);
      console.log('📤 Sending to API:', messages.length, 'messages');
      console.log('📤 Full messages array:', JSON.stringify(messages, null, 2));

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ messages }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', response.status, errorText);
        throw new Error('Failed to get chat response');
      }

      const responseData = await response.json();
      console.log('📥 Raw API response:');
      console.log(responseData);

      const assistantContent = responseData.content;
      console.log('📥 Assistant content:');
      console.log(assistantContent);

      // Add assistant message to chat
      const assistantMessage: ChatMessage = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: assistantContent,
        timestamp: new Date(),
      };

      const updatedMessages = [...currentMessages, assistantMessage];
      setChatMessages(updatedMessages);
      console.log('✅ Updated chat messages, total:', updatedMessages.length);
    } catch (error) {
      console.error('❌ Chat error:', error);
      toast.error('Failed to get AI response');
    } finally {
      setIsChatLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      role: 'user',
      content: inputValue,
      timestamp: new Date(),
    };

    const updatedMessages = [...chatMessages, userMessage];
    setChatMessages(updatedMessages);
    setInputValue('');

    // Send to AI
    await sendChatMessage(inputValue, updatedMessages);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">🔍 Test Chat Debug</h1>

          {/* Mock Analysis Context Display */}
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">📊 Mock Analysis Data</h3>
            <p className="text-sm text-blue-700">
              <strong>Transcript:</strong> "{mockAnalysisData.transcript}"
            </p>
            <p className="text-sm text-blue-700">
              <strong>Top Emotions:</strong> {mockAnalysisData.emotions.slice(0, 3).map(e =>
                `${e.name} (${(e.score * 100).toFixed(1)}%)`
              ).join(', ')}
            </p>
          </div>

          {/* Chat Messages */}
          <div className="space-y-4 mb-6 max-h-96 overflow-y-auto border rounded-lg p-4 bg-gray-50">
            {chatMessages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-800'
                    }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs opacity-75 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}

            {/* Loading state */}
            {isChatLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-800 px-4 py-3 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                    <span className="text-sm">AI is thinking...</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Chat Input */}
          <div className="flex space-x-2">
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isChatLoading}
            />
            <button
              onClick={handleSendMessage}
              disabled={isChatLoading || !inputValue.trim()}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isChatLoading ? 'Sending...' : 'Send'}
            </button>
          </div>

          {/* Debug Info */}
          <div className="mt-4 text-xs text-gray-500">
            <p>💡 Check browser console for detailed debug logs</p>
            <p>📊 Total messages: {chatMessages.length}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
