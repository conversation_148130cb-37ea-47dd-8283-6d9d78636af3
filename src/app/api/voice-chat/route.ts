import { NextRequest, NextResponse } from "next/server";
import { HumeClient } from "hume";
import { AnalysisFactory } from "@/lib/analysis-factory";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const audioFile = formData.get("audio") as File;

    if (!audioFile) {
      return NextResponse.json(
        { error: "No audio file provided" },
        { status: 400 }
      );
    }

    const humeApiKey = process.env.HUME_API_KEY;
    if (!humeApiKey) {
      return NextResponse.json(
        { error: "Hume API key not configured" },
        { status: 500 }
      );
    }

    // Initialize Hume client
    const hume = new HumeClient({
      apiKey: humeApiKey,
    });

    // Submit job using Hume SDK with local file and transcription
    const jobResponse =
      await hume.expressionMeasurement.batch.startInferenceJobFromLocalFile(
        [audioFile],
        {
          json: {
            models: {
              prosody: {},
              burst: {},
            },
            transcription: {
              language: "en",
            },
          },
        }
      );

    const jobId = jobResponse.jobId;

    // Wait for job completion by polling
    let jobCompleted = false;
    let attempts = 0;
    const maxAttempts = 30; // 30 seconds timeout

    while (!jobCompleted && attempts < maxAttempts) {
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait 1 second

      const jobDetails = await hume.expressionMeasurement.batch.getJobDetails(
        jobId
      );

      if (jobDetails.state.status === "COMPLETED") {
        jobCompleted = true;
      } else if (jobDetails.state.status === "FAILED") {
        return NextResponse.json(
          { error: "Voice analysis job failed" },
          { status: 500 }
        );
      }

      attempts++;
    }

    if (!jobCompleted) {
      return NextResponse.json(
        { error: "Voice analysis timed out" },
        { status: 408 }
      );
    }

    // Get predictions using SDK
    const predictions =
      await hume.expressionMeasurement.batch.getJobPredictions(jobId);

    // Process the response using AnalysisFactory
    const processedResponse = AnalysisFactory.processHumeResponse(
      { results: predictions },
      "original", // Use original analysis type for chat
      0.0, // No threshold filtering for chat
      10 // Get top 10 emotions
    );

    return NextResponse.json({
      transcript: processedResponse.transcript.trim(),
      emotions: processedResponse.emotions.slice(0, 5), // Return top 5 emotions for chat
    });
  } catch (error) {
    console.error("Voice chat analysis error:", error);
    return NextResponse.json(
      { error: "Internal server error during voice analysis" },
      { status: 500 }
    );
  }
}
