'use client';

import { useState, useEffect, useCallback } from 'react';
import toast from 'react-hot-toast';
import { AnalysisSettings, AnalysisData, ChatMessage } from '@/types/analysis';
import FeedbackModal from './FeedbackModal';
import EmotionDisplay from './EmotionDisplay';
import VoiceChat from './VoiceChat';

interface ResultsPageProps {
  audioBlob: Blob;
  settings: AnalysisSettings;
}

export default function ResultsPage({ audioBlob, settings }: ResultsPageProps) {
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(true);
  const [isGeneratingInsights, setIsGeneratingInsights] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [savedResultId, setSavedResultId] = useState<string | null>(null);
  const [linkCopied, setLinkCopied] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  const analyzeAudio = useCallback(async () => {
    try {
      // Step 1: Analyze emotions and get transcript from Hume
      toast.loading('Analyzing emotions and transcribing...', { id: 'analyze' });

      const emotionFormData = new FormData();
      emotionFormData.append('audio', audioBlob, 'recording.webm');
      emotionFormData.append('emotionThreshold', settings.emotionThreshold.toString());
      emotionFormData.append('maxEmotions', settings.maxEmotions.toString());
      emotionFormData.append('analysisType', settings.analysisType);

      const emotionResponse = await fetch('/api/analyze-emotions', {
        method: 'POST',
        body: emotionFormData,
      });

      if (!emotionResponse.ok) {
        throw new Error('Failed to analyze emotions');
      }

      const { emotions, analyzedEmotions, sentenceEmotions, transcript, analysisType, displayType } = await emotionResponse.json();
      toast.success('Analysis and transcription complete!', { id: 'analyze' });

      // Update state with partial data
      setAnalysisData({
        transcript,
        emotions,
        analyzedEmotions,
        sentenceEmotions,
        insights: '',
        analysisType: analysisType || settings.analysisType,
        displayType: displayType || 'standard'
      });
      setIsAnalyzing(false);
      setIsGeneratingInsights(true);

      // Step 2: Generate insights
      toast.loading('Generating AI insights...', { id: 'insights' });

      const insightResponse = await fetch('/api/generate-insights', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transcript,
          topEmotions: analyzedEmotions,
          analysisType: settings.analysisType,
          sentenceEmotions,
        }),
      });

      if (!insightResponse.ok) {
        throw new Error('Failed to generate insights');
      }

      const { insights } = await insightResponse.json();
      toast.success('Analysis complete!', { id: 'insights' });

      // Update with complete data
      setAnalysisData({
        transcript,
        emotions,
        analyzedEmotions,
        sentenceEmotions,
        insights,
        analysisType: analysisType || settings.analysisType,
        displayType: displayType || 'standard'
      });
      setIsGeneratingInsights(false);

      // Step 3: Save results to database
      setIsSaving(true);
      toast.loading('Saving results...', { id: 'save' });

      try {
        const saveResponse = await fetch('/api/results', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            inputText: transcript,
            analysisText: insights,
            analyzedEmotions: analyzedEmotions,
            analysisType: settings.analysisType,
            chatData: chatMessages.length > 0 ? { messages: chatMessages } : null,
          }),
        });

        if (saveResponse.ok) {
          const { id } = await saveResponse.json();
          setSavedResultId(id);
          toast.success('Results saved!', { id: 'save' });
        } else {
          throw new Error('Failed to save results');
        }
      } catch (saveError) {
        console.error('Save error:', saveError);
        toast.error('Failed to save results, but analysis is complete', { id: 'save' });
      } finally {
        setIsSaving(false);
      }

    } catch (error) {
      console.error('Analysis error:', error);
      toast.error('Analysis failed. Please try again.');
      setIsAnalyzing(false);
      setIsGeneratingInsights(false);
    }
  }, [audioBlob, settings.emotionThreshold, settings.maxEmotions, settings.analysisType]);

  useEffect(() => {
    analyzeAudio();
  }, [analyzeAudio]);

  const copyResultLink = async () => {
    if (!savedResultId) return;

    const baseUrl = window.location.origin;
    const resultUrl = `${baseUrl}/results/${savedResultId}`;

    try {
      await navigator.clipboard.writeText(resultUrl);
      setLinkCopied(true);
      toast.success('Link copied to clipboard!');

      // Reset the copied state after 3 seconds
      setTimeout(() => {
        setLinkCopied(false);
      }, 3000);
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error('Failed to copy link');
    }
  };



  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      {/* Transcript Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">
          Transcript
        </h3>
        <div className="bg-gray-50 rounded-lg p-4">
          {isAnalyzing ? (
            <div className="flex items-center space-x-3">
              <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
              <span className="text-gray-600">Transcribing and analyzing...</span>
            </div>
          ) : (
            <p className="text-gray-700 leading-relaxed">
              {analysisData?.transcript || 'No transcript available'}
            </p>
          )}
        </div>
      </div>

      {/* Emotions Section */}
      {isAnalyzing ? (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            Detected Emotions
          </h3>
          <div className="flex items-center space-x-3">
            <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="text-gray-600">Analyzing emotional patterns...</span>
          </div>
        </div>
      ) : analysisData ? (
        <EmotionDisplay analysisData={analysisData} />
      ) : null}

      {/* AI Insights Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold text-gray-800 mb-4">
          AI Emotional Insights
        </h3>
        <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg p-6">
          {isGeneratingInsights ? (
            <div className="flex items-center space-x-3">
              <div className="animate-spin w-5 h-5 border-2 border-purple-500 border-t-transparent rounded-full"></div>
              <span className="text-gray-600">Generating personalized insights...</span>
            </div>
          ) : analysisData?.insights ? (
            <div className="prose prose-gray max-w-none">
              {analysisData.insights.split('\n').map((paragraph, index) => (
                paragraph.trim() && (
                  <p key={index} className="text-gray-700 leading-relaxed mb-4 last:mb-0">
                    {paragraph.trim()}
                  </p>
                )
              ))}
            </div>
          ) : (
            <p className="text-gray-600">Insights will appear here once analysis is complete.</p>
          )}
        </div>

        {/* Action Buttons - Only show after insights are generated */}
        {savedResultId && analysisData?.insights && (
          <div className="mt-6 flex flex-col items-center space-y-4">
            <div className="flex space-x-3">
              <button
                onClick={copyResultLink}
                className={`px-6 py-3 rounded-lg transition-colors font-medium cursor-pointer ${linkCopied
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-600 text-white hover:bg-gray-700'
                  }`}
              >
                {linkCopied ? 'Link Copied!' : 'Copy Link'}
              </button>
              <button
                onClick={() => setShowFeedbackModal(true)}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium cursor-pointer"
              >
                💬 Feedback
              </button>
            </div>
            <p className="text-sm text-gray-500 text-center">
              Anyone with the link can access this analysis
            </p>
          </div>
        )}
      </div>

      {/* Voice Chat Section - Only show after insights are generated */}
      {analysisData?.insights && (
        <VoiceChat analysisData={analysisData} onChatUpdate={setChatMessages} />
      )}

      {/* Action Buttons */}
      <div className="flex flex-col items-center space-y-4">
        <div className="flex justify-center space-x-4">
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            Analyze Another Recording
          </button>
        </div>
      </div>

      {/* Save Status */}
      {isSaving && (
        <div className="text-center">
          <div className="flex items-center justify-center space-x-3">
            <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="text-gray-600">Saving your analysis...</span>
          </div>
        </div>
      )}

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={showFeedbackModal}
        onClose={() => setShowFeedbackModal(false)}
        resultId={savedResultId || undefined}
      />
    </div>
  );
}
