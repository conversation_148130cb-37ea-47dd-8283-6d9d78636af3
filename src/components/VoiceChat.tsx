'use client';

import { useState, useRef, useCallback } from 'react';
import { useChat } from 'ai/react';
import { AudioRecorder, formatDuration } from '@/lib/audio-utils';
import { ChatMessage, EmotionScore, AnalysisData } from '@/types/analysis';
import toast from 'react-hot-toast';

interface VoiceChatProps {
  analysisData: AnalysisData;
  onChatUpdate?: (messages: ChatMessage[]) => void;
}

export default function VoiceChat({ analysisData, onChatUpdate }: VoiceChatProps) {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  const audioRecorderRef = useRef<AudioRecorder | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const [isChatLoading, setIsChatLoading] = useState(false);

  const generateAnalysisContext = () => {
    const top10Emotions = analysisData.emotions.slice(0, 10)
      .map((emotion, index) => `${index + 1}. ${emotion.name} (${(emotion.score * 100).toFixed(1)}%)`)
      .join('\n');

    let context = `You are an expert emotional intelligence coach. The user has just completed a voice emotion analysis. Here are their results:

ORIGINAL TRANSCRIPT:
"${analysisData.transcript}"

TOP 10 EMOTIONS DETECTED:
${top10Emotions}

AI INSIGHTS GENERATED:
${analysisData.insights}

ANALYSIS TYPE: ${analysisData.analysisType}`;

    // Add sentence-level emotions if available
    if (analysisData.sentenceEmotions && analysisData.sentenceEmotions.length > 0) {
      context += `\n\nSENTENCE-LEVEL EMOTION BREAKDOWN:`;
      analysisData.sentenceEmotions.forEach((sentenceEmotion, index) => {
        const topEmotions = sentenceEmotion.emotions.slice(0, 3)
          .map(e => `${e.name} (${(e.score * 100).toFixed(1)}%)`)
          .join(', ');
        context += `\n${index + 1}. "${sentenceEmotion.sentence}" - ${topEmotions}`;
      });
    }

    context += `\n\nThe user will now chat with you using voice messages. Each message will include both their spoken words and the emotions detected in their voice. Please provide thoughtful, empathetic responses that acknowledge both their words and emotional state. Help them explore their feelings and provide insights about their emotional patterns.`;

    return context;
  };

  const sendChatMessage = async (content: string, currentMessages: ChatMessage[]) => {
    setIsChatLoading(true);

    try {
      // Create context from initial analysis
      const analysisContext = generateAnalysisContext();
      console.log('🔍 Analysis context generated:');
      console.log(analysisContext);

      // Build messages array with context
      const messages = [];

      // Add context message if this is the first message
      if (currentMessages.length === 0) {
        messages.push({
          role: 'user',
          content: analysisContext,
        });
        console.log('📝 Added context as first message');
      }

      // Add previous chat messages
      messages.push(...currentMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
      })));
      console.log('💬 Added previous messages:', currentMessages.length);

      // Add current user message
      messages.push({
        role: 'user',
        content,
      });
      console.log('🗣️ Added current message:', content);
      console.log('📤 Sending to API:', messages.length, 'messages');
      console.log('📤 Full messages array:');
      console.log(JSON.stringify(messages, null, 2));

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ messages }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API Error:', response.status, errorText);
        throw new Error('Failed to get chat response');
      }

      const responseData = await response.json();
      console.log('📥 Raw API response:');
      console.log(responseData);

      const assistantContent = responseData.content;
      console.log('📥 Assistant content:');
      console.log(assistantContent);

      // Add assistant message to chat
      const assistantMessage: ChatMessage = {
        id: `assistant-${Date.now()}`,
        role: 'assistant',
        content: assistantContent,
        timestamp: new Date(),
      };

      const updatedMessages = [...currentMessages, assistantMessage];
      setChatMessages(updatedMessages);
      onChatUpdate?.(updatedMessages);
      console.log('✅ Chat updated, total messages:', updatedMessages.length);
    } catch (error) {
      console.error('Chat error:', error);
      toast.error('Failed to get AI response');
    } finally {
      setIsChatLoading(false);
    }
  };

  const startRecording = async () => {
    try {
      if (!audioRecorderRef.current) {
        audioRecorderRef.current = new AudioRecorder();
      }

      await audioRecorderRef.current.startRecording();
      setIsRecording(true);
      setRecordingTime(0);

      // Start timer
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

      toast.success('Recording started');
    } catch (error) {
      toast.error('Failed to start recording. Please check microphone permissions.');
      console.error('Recording error:', error);
    }
  };

  const stopRecording = async () => {
    try {
      if (!audioRecorderRef.current) return;

      const audioBlob = await audioRecorderRef.current.stopRecording();
      setIsRecording(false);

      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }

      // Process the voice input
      await processVoiceInput(audioBlob);
    } catch (error) {
      toast.error('Failed to stop recording');
      console.error('Stop recording error:', error);
    }
  };

  const processVoiceInput = async (audioBlob: Blob) => {
    setIsProcessingVoice(true);
    toast.loading('Processing your voice...', { id: 'voice-processing' });

    try {
      // Send audio to voice chat API
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');

      const response = await fetch('/api/voice-chat', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to process voice');
      }

      const { transcript, emotions }: { transcript: string; emotions: EmotionScore[] } = await response.json();

      toast.success('Voice processed!', { id: 'voice-processing' });

      // Create user message with transcript and emotions
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        role: 'user',
        content: transcript,
        emotions,
        timestamp: new Date(),
      };

      // Update chat messages
      const updatedMessages = [...chatMessages, userMessage];
      setChatMessages(updatedMessages);

      // Create enhanced prompt for the AI that includes emotions
      const emotionContext = emotions.length > 0
        ? `\n\n[Detected emotions: ${emotions.map(e => `${e.name} (${(e.score * 100).toFixed(1)}%)`).join(', ')}]`
        : '';

      const enhancedContent = transcript + emotionContext;

      // Send to AI chat
      await sendChatMessage(enhancedContent, updatedMessages);

      onChatUpdate?.(updatedMessages);
    } catch (error) {
      console.error('Voice processing error:', error);
      toast.error('Failed to process voice input', { id: 'voice-processing' });
    } finally {
      setIsProcessingVoice(false);
    }
  };

  const formatEmotions = (emotions: EmotionScore[]) => {
    if (!emotions || emotions.length === 0) return null;

    return (
      <div className="text-xs text-gray-500 mt-1 flex flex-wrap gap-1">
        {emotions.slice(0, 3).map((emotion, index) => (
          <span key={index} className="bg-gray-100 px-2 py-1 rounded">
            {emotion.name} {(emotion.score * 100).toFixed(0)}%
          </span>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold text-gray-800 mb-4">
        Voice Chat
      </h3>
      <p className="text-gray-600 mb-6">
        Speak naturally and I'll respond with insights about your emotions and thoughts.
      </p>

      {/* Chat Messages */}
      <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
        {chatMessages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${message.role === 'user'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-100 text-gray-800'
                }`}
            >
              <p className="text-sm">{message.content}</p>
              {message.role === 'user' && message.emotions && formatEmotions(message.emotions)}
            </div>
          </div>
        ))}

        {/* Show loading state for AI response */}
        {isChatLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 text-gray-800 px-4 py-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="animate-spin w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                <span className="text-sm">Thinking...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Recording Status */}
      {isRecording && (
        <div className="mb-4 bg-red-50 rounded-xl p-4 text-center">
          <div className="text-2xl font-mono text-red-600 mb-2">
            {formatDuration(recordingTime)}
          </div>
          <div className="flex items-center justify-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-red-600 font-medium">Recording...</span>
          </div>
        </div>
      )}

      {/* Voice Input Button */}
      <div className="flex justify-center">
        <button
          onClick={isRecording ? stopRecording : startRecording}
          disabled={isProcessingVoice || isChatLoading}
          className={`
            w-20 h-20 rounded-full border-4 transition-all duration-200
            ${isRecording
              ? 'bg-red-500 border-red-600 hover:bg-red-600'
              : 'bg-blue-500 border-blue-600 hover:bg-blue-600'
            }
            ${(isProcessingVoice || isChatLoading) ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}
            text-white font-semibold shadow-xl
          `}
        >
          {isProcessingVoice || isChatLoading ? (
            <div className="animate-spin w-6 h-6 border-2 border-white border-t-transparent rounded-full mx-auto"></div>
          ) : isRecording ? (
            <div className="w-6 h-6 bg-white rounded mx-auto"></div>
          ) : (
            <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 715 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </div>

      <div className="mt-4 text-center">
        <p className="text-sm text-gray-600">
          {isRecording ? 'Click to stop recording' : 'Click to start speaking'}
        </p>
      </div>
    </div>
  );
}
