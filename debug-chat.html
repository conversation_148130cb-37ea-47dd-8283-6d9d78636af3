<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Chat Context</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .chat-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            margin-left: 20%;
        }
        .assistant-message {
            background-color: #e9ecef;
            color: #333;
            margin-right: 20%;
        }
        .system-message {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            font-size: 0.9em;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .context-section {
            background-color: #e7f3ff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 10px;
            margin-top: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <h1>🔍 Debug Chat Context</h1>
        
        <div class="context-section">
            <h3>📊 Simulated Analysis Context</h3>
            <p><strong>Original Transcript:</strong> "I feel really excited about this new project but also a bit nervous about the challenges ahead."</p>
            <p><strong>Top Emotions:</strong> Excitement (85.2%), Nervousness (72.1%), Anticipation (68.9%)</p>
            <p><strong>AI Insights:</strong> The user shows a healthy balance of enthusiasm and caution, indicating emotional maturity.</p>
        </div>

        <div class="messages" id="messages">
            <!-- Messages will appear here -->
        </div>

        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message..." />
            <button onclick="sendMessage()" id="sendButton">Send</button>
        </div>

        <div class="debug-info" id="debugInfo">
            Debug info will appear here...
        </div>
    </div>

    <script>
        let messages = [];
        let isFirstMessage = true;

        const analysisContext = `You are an expert emotional intelligence coach. The user has just completed a voice emotion analysis. Here are their results:

ORIGINAL TRANSCRIPT:
"I feel really excited about this new project but also a bit nervous about the challenges ahead."

TOP 10 EMOTIONS DETECTED:
1. Excitement (85.2%)
2. Nervousness (72.1%)
3. Anticipation (68.9%)

AI INSIGHTS GENERATED:
The user shows a healthy balance of enthusiasm and caution, indicating emotional maturity and readiness for growth.

The user will now chat with you. Please provide thoughtful, empathetic responses that acknowledge their emotional state and help them explore their feelings.`;

        function addMessage(role, content, isDebug = false) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}-message`;
            
            if (isDebug) {
                messageDiv.innerHTML = `<strong>[${role.toUpperCase()}]:</strong><br>${content}`;
            } else {
                messageDiv.textContent = content;
            }
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateDebugInfo(info) {
            document.getElementById('debugInfo').textContent = info;
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const userMessage = input.value.trim();
            
            if (!userMessage) return;

            // Disable input
            input.disabled = true;
            sendButton.disabled = true;
            
            // Add user message to display
            addMessage('user', userMessage);
            
            // Build messages array for API
            const apiMessages = [];
            
            // Add system context if this is the first message
            if (isFirstMessage) {
                apiMessages.push({
                    role: 'user', // Changed from 'system' to 'user' as per the diff
                    content: analysisContext
                });
                addMessage('system', 'Context added: ' + analysisContext.substring(0, 100) + '...', true);
                isFirstMessage = false;
            }
            
            // Add previous messages
            apiMessages.push(...messages);
            
            // Add current message
            apiMessages.push({
                role: 'user',
                content: userMessage
            });

            // Update debug info
            updateDebugInfo(`Sending ${apiMessages.length} messages to API:\n${JSON.stringify(apiMessages, null, 2)}`);

            try {
                addMessage('assistant', 'Thinking...', false);
                
                const response = await fetch('http://localhost:3000/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ messages: apiMessages })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                
                // Remove "Thinking..." message
                const messagesDiv = document.getElementById('messages');
                messagesDiv.removeChild(messagesDiv.lastChild);
                
                // Add assistant response
                addMessage('assistant', data.content);
                
                // Update messages array
                messages.push(
                    { role: 'user', content: userMessage },
                    { role: 'assistant', content: data.content }
                );

                updateDebugInfo(`Response received: ${data.content.length} characters`);

            } catch (error) {
                console.error('Chat error:', error);
                // Remove "Thinking..." message
                const messagesDiv = document.getElementById('messages');
                messagesDiv.removeChild(messagesDiv.lastChild);
                
                addMessage('assistant', `Error: ${error.message}`);
                updateDebugInfo(`Error: ${error.message}`);
            }

            // Re-enable input
            input.disabled = false;
            sendButton.disabled = false;
            input.value = '';
            input.focus();
        }

        // Allow Enter key to send message
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // Focus input on load
        window.onload = function() {
            document.getElementById('messageInput').focus();
        };
    </script>
</body>
</html>
