const puppeteer = require('puppeteer');

async function testChatInteraction() {
  console.log('🚀 Starting chat interaction test...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    devtools: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Listen to console logs from the page
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      if (type === 'log' || type === 'error') {
        console.log(`[BROWSER ${type.toUpperCase()}]:`, text);
      }
    });
    
    console.log('📱 Navigating to test chat page...');
    await page.goto('http://localhost:3000/test-chat', { waitUntil: 'networkidle0' });
    
    console.log('⏳ Waiting for page to load...');
    await page.waitForSelector('input[type="text"]', { timeout: 10000 });
    
    console.log('💬 Typing test message...');
    await page.type('input[type="text"]', 'Can you help me understand my emotions better?');
    
    console.log('🔘 Clicking send button...');
    await page.click('button:not([disabled])');
    
    console.log('⏳ Waiting for response...');
    await page.waitForFunction(
      () => document.querySelectorAll('.bg-gray-100').length > 0,
      { timeout: 30000 }
    );
    
    console.log('✅ Response received! Checking content...');
    
    // Get all messages
    const messages = await page.evaluate(() => {
      const messageElements = document.querySelectorAll('.bg-blue-500, .bg-gray-100');
      return Array.from(messageElements).map(el => ({
        type: el.classList.contains('bg-blue-500') ? 'user' : 'assistant',
        content: el.textContent.trim()
      }));
    });
    
    console.log('📝 Messages found:', messages.length);
    messages.forEach((msg, i) => {
      console.log(`${i + 1}. [${msg.type.toUpperCase()}]: ${msg.content.substring(0, 100)}...`);
    });
    
    // Check if the assistant response mentions the analysis data
    const assistantMessage = messages.find(m => m.type === 'assistant');
    if (assistantMessage) {
      const content = assistantMessage.content.toLowerCase();
      const hasExcitement = content.includes('excitement') || content.includes('85');
      const hasNervousness = content.includes('nervous') || content.includes('72');
      const hasProject = content.includes('project');
      
      console.log('🔍 Context check:');
      console.log('  - Mentions excitement/85%:', hasExcitement);
      console.log('  - Mentions nervousness/72%:', hasNervousness);
      console.log('  - Mentions project:', hasProject);
      
      if (hasExcitement && hasNervousness && hasProject) {
        console.log('✅ SUCCESS: Context is working! AI referenced the analysis data.');
      } else {
        console.log('❌ ISSUE: Context may not be working properly.');
      }
    }
    
    // Keep browser open for manual inspection
    console.log('🔍 Browser will stay open for manual inspection. Press Ctrl+C to close.');
    await new Promise(() => {}); // Keep running
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    // Don't close browser automatically for debugging
    // await browser.close();
  }
}

testChatInteraction().catch(console.error);
