{"name": "hume-emotion-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate:dev": "npx prisma generate && npx prisma migrate dev"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@prisma/client": "^6.11.1", "@types/uuid": "^10.0.0", "ai": "^4.3.16", "dotenv": "^17.0.1", "form-data": "^4.0.3", "hume": "^0.11.3", "next": "15.3.4", "node-fetch": "^3.3.2", "puppeteer": "^24.11.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prisma": "^6.11.1", "tailwindcss": "^4", "typescript": "^5"}}